# This file is deprecated. Tools should instead consume 
# `.dart_tool/package_config.json`.
# 
# For more info see: https://dart.dev/go/dot-packages-deprecation
# 
# Generated by pub on 2025-09-16 22:02:37.930970.
async:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/async-2.8.1/lib/
boolean_selector:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/boolean_selector-2.1.0/lib/
characters:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/characters-1.1.0/lib/
charcode:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/charcode-1.3.1/lib/
clock:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/clock-1.1.0/lib/
collection:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/collection-1.15.0/lib/
cupertino_icons:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/cupertino_icons-1.0.5/lib/
fake_async:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/fake_async-1.2.0/lib/
flutter:file:///Users/<USER>/Developer/flutter/packages/flutter/lib/
flutter_lints:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/flutter_lints-1.0.4/lib/
flutter_test:file:///Users/<USER>/Developer/flutter/packages/flutter_test/lib/
lints:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/lints-1.0.1/lib/
matcher:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/matcher-0.12.10/lib/
meta:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/meta-1.7.0/lib/
path:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/path-1.8.0/lib/
sky_engine:file:///Users/<USER>/Developer/flutter/bin/cache/pkg/sky_engine/lib/
source_span:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/source_span-1.8.1/lib/
stack_trace:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/stack_trace-1.10.0/lib/
stream_channel:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/stream_channel-2.1.0/lib/
string_scanner:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/string_scanner-1.1.0/lib/
term_glyph:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/term_glyph-1.2.0/lib/
test_api:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/test_api-0.4.2/lib/
typed_data:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/typed_data-1.3.0/lib/
vector_math:file:///Users/<USER>/Developer/flutter/.pub-cache/hosted/pub.dartlang.org/vector_math-2.1.0/lib/
flutter_demo_app_v2:lib/

{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "18c1723432283e0cc55f10a6dcfd9e0288a783a885d8b0b3beb2e9f90bde3f49"}], "containerPath": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "configurationName": "Debug", "overrides": {"commandLine": {"table": {"COMPILER_INDEX_STORE_ENABLE": "NO", "FLUTTER_SUPPRESS_ANALYTICS": "true", "SDKROOT": "iphonesimulator18.2"}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO"}}}}, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": true}
{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build": {"is-mutated": true}, "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app": {"is-mutated": true}, "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner": {"is-mutated": true}, "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/_CodeSignature", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<Linked Binary /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner>", "<target-Runner-****************************************************************--begin-scanning>", "<target-Runner-****************************************************************--end>", "<target-Runner-****************************************************************--linker-inputs-ready>", "<target-Runner-****************************************************************--modules-ready>", "<workspace-Debug--stale-file-removal>"], "outputs": ["<all>"]}, "<target-Runner-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/ssu/root.ssu.yaml", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/_CodeSignature", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Assets.car", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/PkgInfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o.scan", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o.scan", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.DependencyMetadataFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"], "roots": ["/tmp/Runner.dst", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build"], "outputs": ["<target-Runner-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>"]}, "<workspace-Debug--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "inputs": [], "outputs": ["/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<ClangStatCache /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-o", "/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner.xcodeproj", "signature": "253e83885065dcb5b46acbb917fe49aa"}, "P0:::CreateBuildDirectory /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build"]}, "P0:::CreateBuildDirectory /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-Runner-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Metadata.appintents>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.DependencyMetadataFileList"], "outputs": ["<target-Runner-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-ChangePermissions>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-StripSymbols>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "outputs": ["<target-Runner-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-GenerateStubAPI>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-CodeSign>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "outputs": ["<target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-Validate>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "outputs": ["<target-Runner-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-CopyAside>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-Runner-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "outputs": ["<target-Runner-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-Runner-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--GeneratedFilesTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ProductStructureTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-Runner-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--RealityAssetsTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap"], "outputs": ["<target-Runner-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/PkgInfo"], "outputs": ["<target-Runner-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--HeadermapTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleMapTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Runner-****************************************************************--InfoPlistTaskProducer>", "<target-Runner-****************************************************************--VersionPlistTaskProducer>", "<target-Runner-****************************************************************--SanitizerTaskProducer>", "<target-Runner-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Runner-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Runner-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Runner-****************************************************************--StubBinaryTaskProducer>", "<target-Runner-****************************************************************--TestTargetTaskProducer>", "<target-Runner-****************************************************************--TestHostTaskProducer>", "<target-Runner-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Runner-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Runner-****************************************************************--DocumentationTaskProducer>", "<target-Runner-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--start>", "<target-Runner-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "outputs": ["<target-Runner-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "outputs": ["<target-Runner-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-Runner-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Runner-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-Runner-****************************************************************--fused-phase0-run-script": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--begin-compiling>", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-****************************************************************->", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"], "outputs": ["<target-Runner-****************************************************************--fused-phase0-run-script>"]}, "P0:::Gate target-Runner-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Assets.car", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o.scan", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o.scan", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "outputs": ["<target-Runner-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Runner-****************************************************************--fused-phase2-copy-files": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--fused-phase2-copy-files>"]}, "P0:::Gate target-Runner-****************************************************************--fused-phase3-thin-binary": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--fused-phase2-copy-files>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--fused-phase3-thin-binary>"]}, "P0:::Gate target-Runner-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Runner-****************************************************************--generated-headers>"]}, "P0:::Gate target-Runner-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"], "outputs": ["<target-Runner-****************************************************************--swift-generated-headers>"]}, "P0:target-Runner-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Metadata.appintents>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist", "--temp-dir-path", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/ssu", "--bundle-id", "com.example.flutterDemoAppV2", "--product-path", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "--extracted-metadata-path", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Metadata.appintents", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "signature": "91126f8793191a49ebefe2aadf74ebc7"}, "P0:target-Runner-****************************************************************-:Debug:CodeSign /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Flutter/AppFrameworkInfo.plist/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/AppDelegate.swift/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/LaunchScreen.storyboard/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/Main.storyboard/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/GeneratedPluginRegistrant.m/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist/", "<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner normal>", "<TRIGGER: MkDir /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/_CodeSignature", "<CodeSign /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "<TRIGGER: CodeSign /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"]}, "P0:target-Runner-****************************************************************-:Debug:CompileAssetCatalog /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalog /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Assets.car"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "12.0", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "control-enabled": false, "deps": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies"], "deps-style": "dependency-info", "signature": "5eb9c5f1f5a75216a7438c3077b1a09e"}, "P0:target-Runner-****************************************************************-:Debug:CompileStoryboard /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"tool": "shell", "description": "CompileStoryboard /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/LaunchScreen.storyboard", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/LaunchScreen.storyboard", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "Runner", "--output-partial-info-plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "--auto-activate-custom-fonts", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "12.0", "--output-format", "human-readable-text", "--compilation-directory", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/LaunchScreen.storyboard"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "control-enabled": false, "signature": "adaf279c94f967bddd0ae1389f569a54"}, "P0:target-Runner-****************************************************************-:Debug:CompileStoryboard /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/Main.storyboard": {"tool": "shell", "description": "CompileStoryboard /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/Main.storyboard", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/Main.storyboard", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "Runner", "--output-partial-info-plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist", "--auto-activate-custom-fonts", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "12.0", "--output-format", "human-readable-text", "--compilation-directory", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Base.lproj/Main.storyboard"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "control-enabled": false, "signature": "f7424898d8d8179be6fbaff77207b0c9"}, "P0:target-Runner-****************************************************************-:Debug:CopyPlistFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Flutter/AppFrameworkInfo.plist": {"tool": "copy-plist", "description": "CopyPlistFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Flutter/AppFrameworkInfo.plist", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Flutter/AppFrameworkInfo.plist", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist"]}, "P0:target-Runner-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "deps": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-Runner-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/AppDelegate.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.DependencyMetadataFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Metadata.appintents>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Runner-****************************************************************--begin-compiling>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Runner-****************************************************************--begin-linking>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--begin-scanning>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--end": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--entry>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Assets.car", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist", "<CopySwiftStdlib /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "<ExtractAppIntentsMetadata /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Metadata.appintents>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/", "<MkDir /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/PkgInfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o.scan", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o.scan", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "<Validate /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-****************************************************************->", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.DependencyMetadataFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh", "<target-Runner-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Runner-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Runner-****************************************************************--Barrier-ChangePermissions>", "<target-Runner-****************************************************************--Barrier-CodeSign>", "<target-Runner-****************************************************************--Barrier-CopyAside>", "<target-Runner-****************************************************************--Barrier-GenerateStubAPI>", "<target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Runner-****************************************************************--Barrier-RegisterProduct>", "<target-Runner-****************************************************************--Barrier-StripSymbols>", "<target-Runner-****************************************************************--Barrier-Validate>", "<target-Runner-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Runner-****************************************************************--DocumentationTaskProducer>", "<target-Runner-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Runner-****************************************************************--GeneratedFilesTaskProducer>", "<target-Runner-****************************************************************--HeadermapTaskProducer>", "<target-Runner-****************************************************************--InfoPlistTaskProducer>", "<target-Runner-****************************************************************--ModuleMapTaskProducer>", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Runner-****************************************************************--ProductStructureTaskProducer>", "<target-Runner-****************************************************************--RealityAssetsTaskProducer>", "<target-Runner-****************************************************************--SanitizerTaskProducer>", "<target-Runner-****************************************************************--StubBinaryTaskProducer>", "<target-Runner-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Runner-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Runner-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Runner-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Runner-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Runner-****************************************************************--TestHostTaskProducer>", "<target-Runner-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-Runner-****************************************************************--TestTargetTaskProducer>", "<target-Runner-****************************************************************--VersionPlistTaskProducer>", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-Runner-****************************************************************--fused-phase2-copy-files>", "<target-Runner-****************************************************************--fused-phase3-thin-binary>", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--swift-generated-headers>"], "outputs": ["<target-Runner-****************************************************************--end>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-Runner-****************************************************************--begin-compiling>"], "outputs": ["<target-Runner-****************************************************************--entry>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Runner.dst>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Runner-****************************************************************--immediate>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList"], "outputs": ["<target-Runner-****************************************************************--linker-inputs-ready>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-****************************************************************->", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"], "outputs": ["<target-Runner-****************************************************************--modules-ready>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--begin-compiling>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/ssu/root.ssu.yaml", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "<ExtractAppIntentsMetadata /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Metadata.appintents>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o.scan", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o.scan", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat", "<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-****************************************************************->", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.DependencyMetadataFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh", "<target-Runner-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-Runner-****************************************************************--unsigned-product-ready>"]}, "P0:target-Runner-****************************************************************-:Debug:Gate target-Runner-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-Runner-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-Runner-****************************************************************--will-sign>"]}, "P0:target-Runner-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets/", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "12.0", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Assets.xcassets", "--bundle-identifier", "com.example.flutterDemoAppV2", "--generate-swift-asset-symbols", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "control-enabled": false, "signature": "bb6a302398c60dea8c993c95a5f687cb"}, "P0:target-Runner-****************************************************************-:Debug:LinkStoryboards": {"tool": "shell", "description": "LinkStoryboards", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "Runner", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "12.0", "--output-format", "human-readable-text", "--link", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "control-enabled": false, "signature": "c8405e1b833de06cf53c2e63fee96c15"}, "P0:target-Runner-****************************************************************-:Debug:MkDir /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "inputs": ["<target-Runner-****************************************************************--start>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "<MkDir /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>", "<TRIGGER: MkDir /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"]}, "P0:target-Runner-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Info.plist", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--entry>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/PkgInfo"]}, "P0:target-Runner-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist", "<target-Runner-****************************************************************--ProductStructureTaskProducer>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent"]}, "P0:target-Runner-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "<target-Runner-****************************************************************--ProductStructureTaskProducer>", "<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "-o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "signature": "0a11f9c43a3265c4db2a7a6391f4d04a"}, "P0:target-Runner-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "<target-Runner-****************************************************************--Barrier-CodeSign>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"]}, "P0:target-Runner-****************************************************************-:Debug:ScanDependencies /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/GeneratedPluginRegistrant.m", "<ClangStatCache /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--swift-generated-headers>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o.scan"]}, "P0:target-Runner-****************************************************************-:Debug:ScanDependencies /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c", "<ClangStatCache /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--swift-generated-headers>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o.scan"]}, "P0:target-Runner-****************************************************************-:Debug:SwiftDriver Compilation Runner normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation Runner normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/AppDelegate.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Runner-Bridging-Header.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap", "<ClangStatCache /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-Runner-****************************************************************-:Debug:Touch /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app": {"tool": "shell", "description": "Touch /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "<target-Runner-****************************************************************--Barrier-Validate>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app"], "env": {}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "signature": "da113443870cd951ed38755643d5b911"}, "P0:target-Runner-****************************************************************-:Debug:Validate /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Info.plist", "<target-Runner-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Runner-****************************************************************--will-sign>", "<target-Runner-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"], "outputs": ["<Validate /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app>"]}, "P1:target-Runner-****************************************************************-:Debug:CompileC /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/GeneratedPluginRegistrant.m", "<ClangStatCache /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o.scan", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--swift-generated-headers>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o"]}, "P1:target-Runner-****************************************************************-:Debug:CompileC /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c", "<ClangStatCache /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o.scan", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--swift-generated-headers>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner-18c1723432283e0cc55f10a6dcfd9e02-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-Runner-****************************************************************-:Debug:Copy /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo/", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-Runner-****************************************************************-:Debug:Copy /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.abi.json /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.abi.json /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json/", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.abi.json"]}, "P2:target-Runner-****************************************************************-:Debug:Copy /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc/", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"]}, "P2:target-Runner-****************************************************************-:Debug:Copy /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule/", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"]}, "P2:target-Runner-****************************************************************-:Debug:Ld /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner normal": {"tool": "shell", "description": "Ld /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner normal", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator", "<target-Runner-****************************************************************--generated-headers>", "<target-Runner-****************************************************************--swift-generated-headers>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner", "<Linked Binary /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-ios12.0-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator", "-F/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator", "-filelist", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-fobjc-arc", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat", "-o", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app/Runner"], "env": {}, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "deps": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat"], "deps-style": "dependency-info", "signature": "76b5d5deaf760c8a30d7274df553b756"}, "P2:target-Runner-****************************************************************-:Debug:PhaseScriptExecution Run Script /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool": "shell", "description": "PhaseScriptExecution Run Script /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh", "<target-Runner-****************************************************************--ModuleVerifierTaskProducer>", "<target-Runner-****************************************************************--entry>"], "outputs": ["<execute-shell-script-18c1723432283e0cc55f10a6dcfd9e029eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-****************************************************************->"], "args": ["/bin/sh", "-c", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"], "env": {"ACTION": "build", "AD_HOC_CODE_SIGNING_ALLOWED": "YES", "AGGREGATE_TRACKED_DOMAINS": "YES", "ALLOW_BUILD_REQUEST_OVERRIDES": "NO", "ALLOW_TARGET_PLATFORM_SPECIALIZATION": "NO", "ALTERNATE_GROUP": "staff", "ALTERNATE_MODE": "u+w,go-w,a+rX", "ALTERNATE_OWNER": "<PERSON><PERSON><PERSON>", "ALTERNATIVE_DISTRIBUTION_WEB": "NO", "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "ALWAYS_SEARCH_USER_PATHS": "NO", "ALWAYS_USE_SEPARATE_HEADERMAPS": "NO", "APPLE_INTERNAL_DEVELOPER_DIR": "/AppleInternal/Developer", "APPLE_INTERNAL_DIR": "/AppleInternal", "APPLE_INTERNAL_DOCUMENTATION_DIR": "/AppleInternal/Documentation", "APPLE_INTERNAL_LIBRARY_DIR": "/AppleInternal/Library", "APPLE_INTERNAL_TOOLS": "/AppleInternal/Developer/Tools", "APPLICATION_EXTENSION_API_ONLY": "NO", "APPLY_RULES_IN_COPY_FILES": "NO", "APPLY_RULES_IN_COPY_HEADERS": "NO", "APP_SHORTCUTS_ENABLE_FLEXIBLE_MATCHING": "YES", "ARCHS": "x86_64", "ARCHS_STANDARD": "arm64 x86_64", "ARCHS_STANDARD_32_64_BIT": "arm64 i386 x86_64", "ARCHS_STANDARD_32_BIT": "i386", "ARCHS_STANDARD_64_BIT": "arm64 x86_64", "ARCHS_STANDARD_INCLUDING_64_BIT": "arm64 x86_64", "ARCHS_UNIVERSAL_IPHONE_OS": "arm64 i386 x86_64", "ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GENERATE_ASSET_SYMBOLS": "YES", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "AUTOMATICALLY_MERGE_DEPENDENCIES": "NO", "AVAILABLE_PLATFORMS": "appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator xros xrsimulator", "AppIdentifierPrefix": "YYD682D2F8.", "BITCODE_GENERATION_MODE": "marker", "BUILD_ACTIVE_RESOURCES_ONLY": "NO", "BUILD_COMPONENTS": "headers build", "BUILD_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build", "BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "BUILD_ROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build", "BUILD_STYLE": "", "BUILD_VARIANTS": "normal", "BUILT_PRODUCTS_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator", "BUNDLE_CONTENTS_FOLDER_PATH_deep": "Contents/", "BUNDLE_EXECUTABLE_FOLDER_NAME_deep": "MacOS", "BUNDLE_EXTENSIONS_FOLDER_PATH": "Extensions", "BUNDLE_FORMAT": "shallow", "BUNDLE_FRAMEWORKS_FOLDER_PATH": "Frameworks", "BUNDLE_PLUGINS_FOLDER_PATH": "PlugIns", "BUNDLE_PRIVATE_HEADERS_FOLDER_PATH": "PrivateHeaders", "BUNDLE_PUBLIC_HEADERS_FOLDER_PATH": "Headers", "CACHE_ROOT": "/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode", "CCHROOT": "/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode", "CHMOD": "/bin/chmod", "CHOWN": "/usr/sbin/chown", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_CACHE_FINE_GRAINED_OUTPUTS": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++0x", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_EXPLICIT_MODULES": "YES", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_MODULES_BUILD_SESSION_FILE": "/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/org.llvm.clang/ModuleCache.noindex/Session.modulevalidation", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "CLASS_FILE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses", "CLEAN_PRECOMPS": "YES", "CLONE_HEADERS": "NO", "COCOAPODS_PARALLEL_CODE_SIGN": "true", "CODESIGNING_FOLDER_PATH": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "CODE_SIGNING_ALLOWED": "YES", "CODE_SIGNING_REQUIRED": "YES", "CODE_SIGN_CONTEXT_CLASS": "XCiPhoneSimulatorCodeSignContext", "CODE_SIGN_IDENTITY": "-", "CODE_SIGN_INJECT_BASE_ENTITLEMENTS": "YES", "COLOR_DIAGNOSTICS": "NO", "COMBINE_HIDPI_IMAGES": "NO", "COMPILATION_CACHE_CAS_PATH": "/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/CompilationCache.noindex", "COMPILATION_CACHE_KEEP_CAS_DIRECTORY": "YES", "COMPILER_INDEX_STORE_ENABLE": "NO", "COMPOSITE_SDK_DIRS": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/CompositeSDKs", "COMPRESS_PNG_FILES": "YES", "CONFIGURATION": "Debug", "CONFIGURATION_BUILD_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator", "CONFIGURATION_TEMP_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator", "CONTENTS_FOLDER_PATH": "Runner.app", "CONTENTS_FOLDER_PATH_SHALLOW_BUNDLE_NO": "Runner.app/Contents", "CONTENTS_FOLDER_PATH_SHALLOW_BUNDLE_YES": "Runner.app", "COPYING_PRESERVES_HFS_DATA": "NO", "COPY_HEADERS_RUN_UNIFDEF": "NO", "COPY_PHASE_STRIP": "NO", "CORRESPONDING_DEVICE_PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform", "CORRESPONDING_DEVICE_PLATFORM_NAME": "iphoneos", "CORRESPONDING_DEVICE_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "CORRESPONDING_DEVICE_SDK_NAME": "iphoneos18.2", "CP": "/bin/cp", "CREATE_INFOPLIST_SECTION_IN_BINARY": "NO", "CURRENT_ARCH": "undefined_arch", "CURRENT_PROJECT_VERSION": "1", "CURRENT_VARIANT": "normal", "DART_DEFINES": "RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==", "DART_OBFUSCATION": "false", "DEAD_CODE_STRIPPING": "YES", "DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEBUG_INFORMATION_VERSION": "compiler-default", "DEFAULT_COMPILER": "com.apple.compilers.llvm.clang.1_0", "DEFAULT_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "DEFAULT_KEXT_INSTALL_PATH": "/System/Library/Extensions", "DEFINES_MODULE": "NO", "DEPLOYMENT_LOCATION": "NO", "DEPLOYMENT_POSTPROCESSING": "NO", "DEPLOYMENT_TARGET_SETTING_NAME": "IPHONEOS_DEPLOYMENT_TARGET", "DEPLOYMENT_TARGET_SUGGESTED_VALUES": "12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0 15.1 15.2 15.3 15.4 15.5 15.6 16.0 16.1 16.2 16.3 16.4 16.5 16.6 17.0 17.1 17.2 17.3 17.4 17.5 17.6 18.0 18.1 18.2", "DERIVED_FILES_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources", "DERIVED_FILE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources", "DERIVED_SOURCES_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources", "DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "DEVELOPER_FRAMEWORKS_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_FRAMEWORKS_DIR_QUOTED": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Library", "DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs", "DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Tools", "DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "DEVELOPMENT_LANGUAGE": "en", "DEVELOPMENT_TEAM": "YYD682D2F8", "DIFF": "/usr/bin/diff", "DOCUMENTATION_FOLDER_PATH": "Runner.app/en.lproj/Documentation", "DONT_GENERATE_INFOPLIST_FILE": "NO", "DSTROOT": "/tmp/Runner.dst", "DT_TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "DWARF_DSYM_FILE_NAME": "Runner.app.dSYM", "DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT": "NO", "DWARF_DSYM_FOLDER_PATH": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator", "DYNAMIC_LIBRARY_EXTENSION": "dylib", "EAGER_COMPILATION_ALLOW_SCRIPTS": "NO", "EAGER_LINKING": "NO", "EFFECTIVE_PLATFORM_NAME": "-iphonesimulator", "EMBEDDED_CONTENT_CONTAINS_SWIFT": "NO", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_APP_SANDBOX": "NO", "ENABLE_BITCODE": "NO", "ENABLE_CODE_COVERAGE": "YES", "ENABLE_DEBUG_DYLIB": "YES", "ENABLE_DEFAULT_HEADER_SEARCH_PATHS": "YES", "ENABLE_DEFAULT_SEARCH_PATHS": "YES", "ENABLE_HARDENED_RUNTIME": "NO", "ENABLE_HEADER_DEPENDENCIES": "YES", "ENABLE_ON_DEMAND_RESOURCES": "YES", "ENABLE_PREVIEWS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "ENTITLEMENTS_ALLOWED": "NO", "ENTITLEMENTS_DESTINATION": "__entitlements", "ENTITLEMENTS_REQUIRED": "NO", "EXCLUDED_ARCHS": "arm64", "EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS": ".DS_Store .svn .git .hg CVS", "EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES": "*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj", "EXECUTABLES_FOLDER_PATH": "Runner.app/Executables", "EXECUTABLE_FOLDER_PATH": "Runner.app", "EXECUTABLE_FOLDER_PATH_SHALLOW_BUNDLE_NO": "Runner.app/MacOS", "EXECUTABLE_FOLDER_PATH_SHALLOW_BUNDLE_YES": "Runner.app", "EXECUTABLE_NAME": "Runner", "EXECUTABLE_PATH": "Runner.app/Runner", "EXPANDED_CODE_SIGN_IDENTITY": "-", "EXPANDED_CODE_SIGN_IDENTITY_NAME": "Sign to Run Locally", "EXTENSIONS_FOLDER_PATH": "Runner.app/Extensions", "FILE_LIST": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList", "FIXED_FILES_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles", "FLUTTER_APPLICATION_PATH": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2", "FLUTTER_BUILD_DIR": "build", "FLUTTER_BUILD_NAME": "1.0.0", "FLUTTER_BUILD_NUMBER": "1", "FLUTTER_ROOT": "/Users/<USER>/Developer/flutter", "FLUTTER_SUPPRESS_ANALYTICS": "true", "FLUTTER_TARGET": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/lib/main.dart", "FRAMEWORKS_FOLDER_PATH": "Runner.app/Frameworks", "FRAMEWORK_FLAG_PREFIX": "-framework", "FRAMEWORK_SEARCH_PATHS": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator ", "FRAMEWORK_VERSION": "A", "FULL_PRODUCT_NAME": "Runner.app", "FUSE_BUILD_PHASES": "YES", "FUSE_BUILD_SCRIPT_PHASES": "NO", "GCC3_VERSION": "3.3", "GCC_C_LANGUAGE_STANDARD": "gnu99", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_INLINES_ARE_PRIVATE_EXTERN": "YES", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OBJC_LEGACY_DISPATCH": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PFE_FILE_C_DIALECTS": "c objective-c c++ objective-c++", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 ", "GCC_SYMBOLS_PRIVATE_EXTERN": "NO", "GCC_TREAT_WARNINGS_AS_ERRORS": "NO", "GCC_VERSION": "com.apple.compilers.llvm.clang.1_0", "GCC_VERSION_IDENTIFIER": "com_apple_compilers_llvm_clang_1_0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "GENERATED_MODULEMAP_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/GeneratedModuleMaps-iphonesimulator", "GENERATE_INFOPLIST_FILE": "NO", "GENERATE_INTERMEDIATE_TEXT_BASED_STUBS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_PKGINFO_FILE": "YES", "GENERATE_PROFILING_CODE": "NO", "GENERATE_TEXT_BASED_STUBS": "NO", "GID": "20", "GROUP": "staff", "HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_TARGETS_NOT_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS": "YES", "HEADERMAP_INCLUDES_PROJECT_HEADERS": "YES", "HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES": "YES", "HEADERMAP_USES_VFS": "NO", "HEADER_SEARCH_PATHS": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/include ", "HIDE_BITCODE_SYMBOLS": "YES", "HOME": "/Users/<USER>", "HOST_ARCH": "arm64", "HOST_PLATFORM": "macosx", "ICONV": "/usr/bin/iconv", "IMPLICIT_DEPENDENCY_DOMAIN": "default", "INFOPLIST_ENABLE_CFBUNDLEICONS_MERGE": "YES", "INFOPLIST_EXPAND_BUILD_SETTINGS": "YES", "INFOPLIST_FILE": "Runner/Info.plist", "INFOPLIST_OUTPUT_FORMAT": "binary", "INFOPLIST_PATH": "Runner.app/Info.plist", "INFOPLIST_PREPROCESS": "NO", "INFOSTRINGS_PATH": "Runner.app/en.lproj/InfoPlist.strings", "INLINE_PRIVATE_FRAMEWORKS": "NO", "INSTALLHDRS_COPY_PHASE": "NO", "INSTALLHDRS_SCRIPT_PHASE": "NO", "INSTALL_DIR": "/tmp/Runner.dst/Applications", "INSTALL_GROUP": "staff", "INSTALL_MODE_FLAG": "u+w,go-w,a+rX", "INSTALL_OWNER": "<PERSON><PERSON><PERSON>", "INSTALL_PATH": "/Applications", "INSTALL_ROOT": "/tmp/Runner.dst", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IS_UNOPTIMIZED_BUILD": "YES", "JAVAC_DEFAULT_FLAGS": "-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8", "JAVA_APP_STUB": "/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub", "JAVA_ARCHIVE_CLASSES": "YES", "JAVA_ARCHIVE_TYPE": "JAR", "JAVA_COMPILER": "/usr/bin/javac", "JAVA_FOLDER_PATH": "Runner.app/Java", "JAVA_FRAMEWORK_RESOURCES_DIRS": "Resources", "JAVA_JAR_FLAGS": "cv", "JAVA_SOURCE_SUBDIR": ".", "JAVA_USE_DEPENDENCIES": "YES", "JAVA_ZIP_FLAGS": "-urg", "JIKES_DEFAULT_FLAGS": "+E +OLDCSO", "KEEP_PRIVATE_EXTERNS": "NO", "LD_DEPENDENCY_INFO_FILE": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat", "LD_ENTITLEMENTS_SECTION": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent", "LD_ENTITLEMENTS_SECTION_DER": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent.der", "LD_EXPORT_SYMBOLS": "YES", "LD_GENERATE_MAP_FILE": "NO", "LD_MAP_FILE_PATH": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt", "LD_NO_PIE": "NO", "LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER": "YES", "LD_RUNPATH_SEARCH_PATHS": " @executable_path/Frameworks", "LD_RUNPATH_SEARCH_PATHS_YES": "@loader_path/../Frameworks", "LD_SHARED_CACHE_ELIGIBLE": "Automatic", "LD_WARN_DUPLICATE_LIBRARIES": "NO", "LD_WARN_UNUSED_DYLIBS": "NO", "LEGACY_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer", "LEX": "lex", "LIBRARY_DEXT_INSTALL_PATH": "/Library/DriverExtensions", "LIBRARY_FLAG_NOSPACE": "YES", "LIBRARY_FLAG_PREFIX": "-l", "LIBRARY_KEXT_INSTALL_PATH": "/Library/Extensions", "LIBRARY_SEARCH_PATHS": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator ", "LINKER_DISPLAYS_MANGLED_NAMES": "NO", "LINK_FILE_LIST_normal_x86_64": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList", "LINK_OBJC_RUNTIME": "YES", "LINK_WITH_STANDARD_LIBRARIES": "YES", "LLVM_TARGET_TRIPLE_OS_VERSION": "ios12.0", "LLVM_TARGET_TRIPLE_SUFFIX": "-simulator", "LLVM_TARGET_TRIPLE_VENDOR": "apple", "LM_AUX_CONST_METADATA_LIST_PATH_normal_x86_64": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList", "LOCALIZATION_EXPORT_SUPPORTED": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "NO", "LOCALIZED_RESOURCES_FOLDER_PATH": "Runner.app/en.lproj", "LOCALIZED_STRING_MACRO_NAMES": "NSLocalizedString CFCopyLocalizedString", "LOCALIZED_STRING_SWIFTUI_SUPPORT": "YES", "LOCAL_ADMIN_APPS_DIR": "/Applications/Utilities", "LOCAL_APPS_DIR": "/Applications", "LOCAL_DEVELOPER_DIR": "/Library/Developer", "LOCAL_LIBRARY_DIR": "/Library", "LOCROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "LOCSYMROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "MACH_O_TYPE": "mh_execute", "MAC_OS_X_PRODUCT_BUILD_VERSION": "24G90", "MAC_OS_X_VERSION_ACTUAL": "150601", "MAC_OS_X_VERSION_MAJOR": "150000", "MAC_OS_X_VERSION_MINOR": "150600", "MAKE_MERGEABLE": "NO", "MERGEABLE_LIBRARY": "NO", "MERGED_BINARY_TYPE": "none", "MERGE_LINKED_LIBRARIES": "NO", "METAL_LIBRARY_FILE_BASE": "default", "METAL_LIBRARY_OUTPUT_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/Runner.app", "MODULES_FOLDER_PATH": "Runner.app/Modules", "MTL_ENABLE_DEBUG_INFO": "YES", "NATIVE_ARCH": "arm64", "NATIVE_ARCH_32_BIT": "arm", "NATIVE_ARCH_64_BIT": "arm64", "NATIVE_ARCH_ACTUAL": "arm64", "NO_COMMON": "YES", "OBJC_ABI_VERSION": "2", "OBJECT_FILE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects", "OBJECT_FILE_DIR_normal": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal", "OBJROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build", "ONLY_ACTIVE_ARCH": "YES", "OS": "MACOS", "OSAC": "/usr/bin/osacompile", "PACKAGE_CONFIG": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/.dart_tool/package_config.json", "PACKAGE_TYPE": "com.apple.package-type.wrapper.application", "PASCAL_STRINGS": "YES", "PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/XCBuild.framework/Versions/A/PlugIns/XCBBuildService.bundle/Contents/PlugIns/XCBSpecifications.ideplugin/Contents/Resources:/Applications/Xcode.app/Contents/SharedFrameworks/XCBuild.framework/Versions/A/PlugIns/XCBBuildService.bundle/Contents/PlugIns/XCBSpecifications.ideplugin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/appleinternal/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/appleinternal/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/Users/<USER>/.rvm/gems/ruby-3.0.0/bin:/Users/<USER>/.rvm/gems/ruby-3.0.0@global/bin:/Users/<USER>/.rvm/rubies/ruby-3.0.0/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion.app/Contents/Public:/usr/local/go/bin:/Users/<USER>/.cargo/bin:/Users/<USER>/fvm/default/bin:/Users/<USER>/.rvm/bin", "PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES": "/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms", "PBDEVELOPMENTPLIST_PATH": "Runner.app/pbdevelopment.plist", "PER_ARCH_OBJECT_FILE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch", "PER_VARIANT_OBJECT_FILE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal", "PKGINFO_FILE_PATH": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo", "PKGINFO_PATH": "Runner.app/PkgInfo", "PLATFORM_DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications", "PLATFORM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin", "PLATFORM_DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library", "PLATFORM_DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs", "PLATFORM_DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools", "PLATFORM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr", "PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform", "PLATFORM_DISPLAY_NAME": "iOS Simulator", "PLATFORM_FAMILY_NAME": "iOS", "PLATFORM_NAME": "iphonesimulator", "PLATFORM_PREFERRED_ARCH": "x86_64", "PLATFORM_PRODUCT_BUILD_VERSION": "22C146", "PLIST_FILE_OUTPUT_FORMAT": "binary", "PLUGINS_FOLDER_PATH": "Runner.app/PlugIns", "PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR": "YES", "PRECOMP_DESTINATION_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders", "PRIVATE_HEADERS_FOLDER_PATH": "Runner.app/PrivateHeaders", "PROCESSED_INFOPLIST_PATH": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Processed-Info.plist", "PRODUCT_BUNDLE_IDENTIFIER": "com.example.flutterDemoAppV2", "PRODUCT_BUNDLE_PACKAGE_TYPE": "APPL", "PRODUCT_MODULE_NAME": "Runner", "PRODUCT_NAME": "Runner", "PRODUCT_SETTINGS_PATH": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Info.plist", "PRODUCT_TYPE": "com.apple.product-type.application", "PROFILING_CODE": "NO", "PROJECT": "Runner", "PROJECT_DERIVED_FILE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/DerivedSources", "PROJECT_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "PROJECT_FILE_PATH": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner.xcodeproj", "PROJECT_GUID": "18c1723432283e0cc55f10a6dcfd9e02", "PROJECT_NAME": "Runner", "PROJECT_TEMP_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build", "PROJECT_TEMP_ROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build", "PROVISIONING_PROFILE_REQUIRED": "NO", "PROVISIONING_PROFILE_REQUIRED_YES_YES": "YES", "PROVISIONING_PROFILE_SUPPORTED": "YES", "PUBLIC_HEADERS_FOLDER_PATH": "Runner.app/Headers", "RECOMMENDED_IPHONEOS_DEPLOYMENT_TARGET": "15.0", "RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS": "YES", "REMOVE_CVS_FROM_RESOURCES": "YES", "REMOVE_GIT_FROM_RESOURCES": "YES", "REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_HG_FROM_RESOURCES": "YES", "REMOVE_STATIC_EXECUTABLES_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_SVN_FROM_RESOURCES": "YES", "RESCHEDULE_INDEPENDENT_HEADERS_PHASES": "YES", "REZ_COLLECTOR_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources", "REZ_OBJECTS_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects", "REZ_SEARCH_PATHS": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator ", "SCAN_ALL_SOURCE_FILES_FOR_INCLUDES": "NO", "SCRIPTS_FOLDER_PATH": "Runner.app/Scripts", "SCRIPT_INPUT_FILE_COUNT": "0", "SCRIPT_INPUT_FILE_LIST_COUNT": "0", "SCRIPT_OUTPUT_FILE_COUNT": "0", "SCRIPT_OUTPUT_FILE_LIST_COUNT": "0", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "SDK_DIR_iphonesimulator": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "SDK_DIR_iphonesimulator18_2": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "SDK_NAME": "iphonesimulator18.2", "SDK_NAMES": "iphonesimulator18.2", "SDK_PRODUCT_BUILD_VERSION": "22C146", "SDK_STAT_CACHE_DIR": "/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode", "SDK_STAT_CACHE_ENABLE": "YES", "SDK_STAT_CACHE_PATH": "/var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "SDK_VERSION": "18.2", "SDK_VERSION_ACTUAL": "180200", "SDK_VERSION_MAJOR": "180000", "SDK_VERSION_MINOR": "180200", "SED": "/usr/bin/sed", "SEPARATE_STRIP": "NO", "SEPARATE_SYMBOL_EDIT": "NO", "SET_DIR_MODE_OWNER_GROUP": "YES", "SET_FILE_MODE_OWNER_GROUP": "NO", "SHALLOW_BUNDLE": "YES", "SHALLOW_BUNDLE_TRIPLE": "ios-simulator", "SHALLOW_BUNDLE_ios_macabi": "NO", "SHALLOW_BUNDLE_macos": "NO", "SHARED_DERIVED_FILE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/DerivedSources", "SHARED_FRAMEWORKS_FOLDER_PATH": "Runner.app/SharedFrameworks", "SHARED_PRECOMPS_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/SharedPrecompiledHeaders", "SHARED_SUPPORT_FOLDER_PATH": "Runner.app/SharedSupport", "SKIP_INSTALL": "NO", "SOURCE_ROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "SRCROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "STRINGSDATA_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch", "STRINGSDATA_ROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build", "STRINGS_FILE_INFOPLIST_RENAME": "YES", "STRINGS_FILE_OUTPUT_ENCODING": "binary", "STRIP_BITCODE_FROM_COPIED_FILES": "NO", "STRIP_INSTALLED_PRODUCT": "NO", "STRIP_STYLE": "all", "STRIP_SWIFT_SYMBOLS": "YES", "SUPPORTED_DEVICE_FAMILIES": "1,2", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator", "SUPPORTS_MACCATALYST": "NO", "SUPPORTS_ON_DEMAND_RESOURCES": "YES", "SUPPORTS_TEXT_BASED_API": "NO", "SUPPRESS_WARNINGS": "NO", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_OBJC_BRIDGING_HEADER": "Runner/Runner-Bridging-Header.h", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_PLATFORM_TARGET_PREFIX": "ios", "SWIFT_RESPONSE_FILE_PATH_normal_x86_64": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "SWIFT_VERSION": "5.0", "SYMROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build", "SYSTEM_ADMIN_APPS_DIR": "/Applications/Utilities", "SYSTEM_APPS_DIR": "/Applications", "SYSTEM_CORE_SERVICES_DIR": "/System/Library/CoreServices", "SYSTEM_DEMOS_DIR": "/Applications/Extras", "SYSTEM_DEVELOPER_APPS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "SYSTEM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "SYSTEM_DEVELOPER_DEMOS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples", "SYSTEM_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SYSTEM_DEVELOPER_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library", "SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools", "SYSTEM_DEVELOPER_JAVA_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Java Tools", "SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools", "SYSTEM_DEVELOPER_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes", "SYSTEM_DEVELOPER_TOOLS": "/Applications/Xcode.app/Contents/Developer/Tools", "SYSTEM_DEVELOPER_TOOLS_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools", "SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools", "SYSTEM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "SYSTEM_DEVELOPER_UTILITIES_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities", "SYSTEM_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "SYSTEM_DOCUMENTATION_DIR": "/Library/Documentation", "SYSTEM_EXTENSIONS_FOLDER_PATH": "Runner.app/SystemExtensions", "SYSTEM_EXTENSIONS_FOLDER_PATH_SHALLOW_BUNDLE_NO": "Runner.app/Library/SystemExtensions", "SYSTEM_EXTENSIONS_FOLDER_PATH_SHALLOW_BUNDLE_YES": "Runner.app/SystemExtensions", "SYSTEM_KEXT_INSTALL_PATH": "/System/Library/Extensions", "SYSTEM_LIBRARY_DIR": "/System/Library", "TAPI_DEMANGLE": "YES", "TAPI_ENABLE_PROJECT_HEADERS": "NO", "TAPI_LANGUAGE": "objective-c", "TAPI_LANGUAGE_STANDARD": "compiler-default", "TAPI_USE_SRCROOT": "YES", "TAPI_VERIFY_MODE": "Pedantic", "TARGETED_DEVICE_FAMILY": "1,2", "TARGETNAME": "Runner", "TARGET_BUILD_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator", "TARGET_NAME": "Runner", "TARGET_TEMP_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build", "TEMP_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build", "TEMP_FILES_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build", "TEMP_FILE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build", "TEMP_ROOT": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build", "TEST_FRAMEWORK_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk/Developer/Library/Frameworks", "TEST_LIBRARY_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib", "TOOLCHAINS": "com.apple.dt.toolchain.XcodeDefault", "TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "TRACK_WIDGET_CREATION": "true", "TREAT_MISSING_BASELINES_AS_TEST_FAILURES": "NO", "TREAT_MISSING_SCRIPT_PHASE_OUTPUTS_AS_ERRORS": "NO", "TREE_SHAKE_ICONS": "false", "TeamIdentifierPrefix": "YYD682D2F8.", "UID": "501", "UNINSTALLED_PRODUCTS_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/UninstalledProducts", "UNLOCALIZED_RESOURCES_FOLDER_PATH": "Runner.app", "UNLOCALIZED_RESOURCES_FOLDER_PATH_SHALLOW_BUNDLE_NO": "Runner.app/Resources", "UNLOCALIZED_RESOURCES_FOLDER_PATH_SHALLOW_BUNDLE_YES": "Runner.app", "UNSTRIPPED_PRODUCT": "NO", "USER": "<PERSON><PERSON><PERSON>", "USER_APPS_DIR": "/Users/<USER>/Applications", "USER_LIBRARY_DIR": "/Users/<USER>/Library", "USE_DYNAMIC_NO_PIC": "YES", "USE_HEADERMAP": "YES", "USE_HEADER_SYMLINKS": "NO", "VALIDATE_DEVELOPMENT_ASSET_PATHS": "YES_ERROR", "VALIDATE_PRODUCT": "NO", "VALID_ARCHS": "arm64 arm64e i386 x86_64", "VERBOSE_PBXCP": "NO", "VERSIONING_SYSTEM": "apple-generic", "VERSIONPLIST_PATH": "Runner.app/version.plist", "VERSION_INFO_BUILDER": "<PERSON><PERSON><PERSON>", "VERSION_INFO_FILE": "Runner_vers.c", "VERSION_INFO_STRING": "\"@(#)PROGRAM:Runner  PROJECT:Runner-1\"", "WORKSPACE_DIR": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner.xcodeproj", "WRAPPER_EXTENSION": "app", "WRAPPER_NAME": "Runner.app", "WRAPPER_SUFFIX": ".app", "WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES": "NO", "XCODE_APP_SUPPORT_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Xcode", "XCODE_PRODUCT_BUILD_VERSION": "16C5032a", "XCODE_VERSION_ACTUAL": "1620", "XCODE_VERSION_MAJOR": "1600", "XCODE_VERSION_MINOR": "1620", "XPCSERVICES_FOLDER_PATH": "Runner.app/XPCServices", "YACC": "yacc", "_WRAPPER_CONTENTS_DIR_SHALLOW_BUNDLE_NO": "/Contents", "_WRAPPER_PARENT_PATH_SHALLOW_BUNDLE_NO": "/..", "_WRAPPER_RESOURCES_DIR_SHALLOW_BUNDLE_NO": "/Resources", "__IS_NOT_MACOS": "YES", "__IS_NOT_MACOS_macosx": "NO", "__IS_NOT_SIMULATOR": "NO", "__IS_NOT_SIMULATOR_simulator": "NO", "arch": "undefined_arch", "variant": "normal"}, "allow-missing-inputs": true, "always-out-of-date": true, "working-directory": "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios", "control-enabled": false, "repair-via-ownership-analysis": true, "signature": "de63e555b03a3508413dff64f91fd06e"}, "P2:target-Runner-****************************************************************-:Debug:SwiftDriver Compilation Requirements Runner normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements Runner normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/AppDelegate.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/Runner/Runner-Bridging-Header.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap", "<ClangStatCache /var/folders/sj/dkzj2xkn1697nrg60j4d3kfh0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-Runner-****************************************************************--copy-headers-completion>", "<target-Runner-****************************************************************--fused-phase0-run-script>", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner Swift Compilation Requirements Finished", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.abi.json", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc"]}, "P2:target-Runner-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "inputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h", "<target-Runner-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftConstValuesFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_const_extract_protocols.json"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.DependencyMetadataFileList", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.DependencyMetadataFileList"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap"]}, "P2:target-Runner-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh", "inputs": ["<target-Runner-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"]}}}
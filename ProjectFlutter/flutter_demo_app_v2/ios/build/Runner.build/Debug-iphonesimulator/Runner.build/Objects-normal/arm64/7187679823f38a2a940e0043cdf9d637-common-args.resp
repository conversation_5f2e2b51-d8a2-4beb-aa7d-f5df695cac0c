-target arm64-apple-ios12.0-simulator '-std=gnu99' -fmodules -gmodules -fpascal-strings -O0 -fno-common '-DDEBUG=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -g -iquote /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap -I/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap -I/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap -iquote /Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap -I/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator/include -I/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/arm64 -I/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/arm64 -I/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources -F/Users/<USER>/ProjectFlutter/flutter_demo_app_v2/ios/build/Debug-iphonesimulator